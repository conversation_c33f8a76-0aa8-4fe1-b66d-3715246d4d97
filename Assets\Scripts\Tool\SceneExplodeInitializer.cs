using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 场景爆炸图初始化器
/// 将所有零件（除了中下段）从中心点向外移动，产生爆炸图效果
/// </summary>
public class SceneExplodeInitializer : MonoBehaviour
{
    [Header("爆炸中心设置")]
    [SerializeField] private Vector3 explodeCenter = new Vector3(-18.735f, 6.935f, 49.4f);
    
    [Header("爆炸距离设置")]
    [SerializeField] private float explodeDistance = 5.0f;
    [Tooltip("爆炸距离倍数，用于调整不同零件的分散程度")]
    [SerializeField] private float distanceMultiplier = 1.0f;
    
    [Header("排除零件设置")]
    [SerializeField] private string[] excludedPartNames = { "中下段" };
    
    [Header("调试设置")]
    [SerializeField] private bool showGizmos = true;
    [SerializeField] private bool debugMode = true;
    
    [Header("控制设置")]
    [SerializeField] private KeyCode initializeKey = KeyCode.E;
    [SerializeField] private KeyCode resetKey = KeyCode.R;
    
    // 存储原始位置，用于重置
    private Dictionary<AssemblyPart, Vector3> originalPositions = new Dictionary<AssemblyPart, Vector3>();
    private bool isExploded = false;

    void Start()
    {
        // 记录所有零件的原始位置
        RecordOriginalPositions();
        
        if (debugMode)
        {
            Debug.Log($"🎆 场景爆炸图初始化器已准备就绪");
            Debug.Log($"📍 爆炸中心: {explodeCenter}");
            Debug.Log($"📏 爆炸距离: {explodeDistance}");
            Debug.Log($"🎮 按 {initializeKey} 键执行爆炸图初始化");
            Debug.Log($"🔄 按 {resetKey} 键重置到原始位置");
        }
    }

    void Update()
    {
        if (Input.GetKeyDown(initializeKey))
        {
            if (!isExploded)
            {
                InitializeExplodeView();
            }
        }
        
        if (Input.GetKeyDown(resetKey))
        {
            if (isExploded)
            {
                ResetToOriginalPositions();
            }
        }
    }

    /// <summary>
    /// 记录所有零件的原始位置
    /// </summary>
    private void RecordOriginalPositions()
    {
        originalPositions.Clear();
        
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        
        foreach (AssemblyPart part in allParts)
        {
            originalPositions[part] = part.PartTransform.position;
            
            if (debugMode)
            {
                Debug.Log($"📝 记录零件原始位置: {part.PartName} -> {part.PartTransform.position}");
            }
        }
        
        Debug.Log($"✅ 已记录 {originalPositions.Count} 个零件的原始位置");
    }

    /// <summary>
    /// 执行爆炸图初始化
    /// </summary>
    public void InitializeExplodeView()
    {
        Debug.Log("🎆 开始执行爆炸图初始化...");
        
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
        int movedCount = 0;
        
        foreach (AssemblyPart part in allParts)
        {
            // 检查是否是排除的零件
            if (IsExcludedPart(part.PartName))
            {
                if (debugMode)
                {
                    Debug.Log($"⏭️ 跳过排除零件: {part.PartName}");
                }
                continue;
            }
            
            // 计算爆炸位置
            Vector3 explodedPosition = CalculateExplodedPosition(part.PartTransform.position);
            
            // 移动零件
            part.PartTransform.position = explodedPosition;
            movedCount++;
            
            if (debugMode)
            {
                Vector3 originalPos = originalPositions.ContainsKey(part) ? originalPositions[part] : part.PartTransform.position;
                float distance = Vector3.Distance(originalPos, explodedPosition);
                Debug.Log($"💥 移动零件: {part.PartName}");
                Debug.Log($"   原始位置: {originalPos}");
                Debug.Log($"   爆炸位置: {explodedPosition}");
                Debug.Log($"   移动距离: {distance:F2}m");
            }
        }
        
        isExploded = true;
        Debug.Log($"✅ 爆炸图初始化完成！移动了 {movedCount} 个零件");
    }

    /// <summary>
    /// 重置到原始位置
    /// </summary>
    public void ResetToOriginalPositions()
    {
        Debug.Log("🔄 重置零件到原始位置...");
        
        int resetCount = 0;
        foreach (var kvp in originalPositions)
        {
            AssemblyPart part = kvp.Key;
            Vector3 originalPosition = kvp.Value;
            
            if (part != null)
            {
                part.PartTransform.position = originalPosition;
                resetCount++;
                
                if (debugMode)
                {
                    Debug.Log($"🔄 重置零件: {part.PartName} -> {originalPosition}");
                }
            }
        }
        
        isExploded = false;
        Debug.Log($"✅ 重置完成！恢复了 {resetCount} 个零件的位置");
    }

    /// <summary>
    /// 计算零件的爆炸位置
    /// </summary>
    /// <param name="originalPosition">零件原始位置</param>
    /// <returns>爆炸后的位置</returns>
    private Vector3 CalculateExplodedPosition(Vector3 originalPosition)
    {
        // 计算从中心点到零件的方向向量
        Vector3 direction = (originalPosition - explodeCenter).normalized;
        
        // 如果零件就在中心点，给一个默认方向
        if (direction.magnitude < 0.001f)
        {
            direction = Vector3.up;
        }
        
        // 计算爆炸后的位置
        Vector3 explodedPosition = explodeCenter + direction * explodeDistance * distanceMultiplier;
        
        return explodedPosition;
    }

    /// <summary>
    /// 检查是否是排除的零件
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <returns>是否排除</returns>
    private bool IsExcludedPart(string partName)
    {
        foreach (string excludedName in excludedPartNames)
        {
            if (partName.Contains(excludedName))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 设置爆炸距离
    /// </summary>
    /// <param name="distance">新的爆炸距离</param>
    public void SetExplodeDistance(float distance)
    {
        explodeDistance = Mathf.Max(0, distance);
        Debug.Log($"🎆 设置爆炸距离: {explodeDistance}");
    }

    /// <summary>
    /// 设置距离倍数
    /// </summary>
    /// <param name="multiplier">新的距离倍数</param>
    public void SetDistanceMultiplier(float multiplier)
    {
        distanceMultiplier = Mathf.Max(0.1f, multiplier);
        Debug.Log($"🎆 设置距离倍数: {distanceMultiplier}");
    }

    /// <summary>
    /// 设置爆炸中心
    /// </summary>
    /// <param name="center">新的爆炸中心</param>
    public void SetExplodeCenter(Vector3 center)
    {
        explodeCenter = center;
        Debug.Log($"🎆 设置爆炸中心: {explodeCenter}");
    }

    void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        // 绘制爆炸中心
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(explodeCenter, 0.5f);
        
        // 绘制爆炸范围
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(explodeCenter, explodeDistance * distanceMultiplier);
        
        // 如果在运行时，绘制零件到中心的连线
        if (Application.isPlaying && debugMode)
        {
            AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();
            
            foreach (AssemblyPart part in allParts)
            {
                if (!IsExcludedPart(part.PartName))
                {
                    Gizmos.color = Color.cyan;
                    Gizmos.DrawLine(explodeCenter, part.PartTransform.position);
                }
            }
        }
    }

    void OnGUI()
    {
        if (!debugMode) return;
        
        GUILayout.BeginArea(new Rect(10, 200, 300, 150));
        GUILayout.Label("爆炸图初始化控制");
        GUILayout.Space(5);
        
        GUILayout.Label($"爆炸中心: {explodeCenter}");
        GUILayout.Label($"爆炸距离: {explodeDistance:F1}");
        GUILayout.Label($"距离倍数: {distanceMultiplier:F1}");
        GUILayout.Label($"状态: {(isExploded ? "已爆炸" : "原始状态")}");
        
        GUILayout.Space(10);
        GUILayout.Label("控制:");
        GUILayout.Label($"{initializeKey} - 执行爆炸图");
        GUILayout.Label($"{resetKey} - 重置位置");
        
        GUILayout.EndArea();
    }
}
