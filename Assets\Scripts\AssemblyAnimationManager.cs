using UnityEngine;
using System.Collections;

/// <summary>
/// 装配动画管理器
///
/// 负责处理所有零件的移动、旋转和动画控制
/// 提供通用的动画方法，支持基于挂载点的精确定位
/// 包含螺丝螺母等特定装配动画的业务逻辑
/// </summary>
public class AssemblyAnimationManager : MonoBehaviour
{
    [Header("动画设置")]
    [SerializeField] private float animationDuration = 1.0f; // 默认动画持续时间

    [Header("螺丝安装设置")]
    [SerializeField] private float screwLength = 0.05f; // 螺丝长度
    [SerializeField] private float nutThickness = 0.01f; // 螺母厚度
    [SerializeField] private float initialDistanceFromHole = 0.1f; // 螺丝初始距离孔位的距离
    [SerializeField] private float screwInitialElevation = 0.05f; // 螺丝初始高度

    [Header("螺丝落点控制")]
    [SerializeField] private float screwInsertionDepth = 0.025f; // 螺丝插入深度（从孔位开始计算）
    [SerializeField] private bool useCustomInsertionDepth = false; // 是否使用自定义插入深度
    [SerializeField] private float screwInsertionRatio = 0.5f; // 螺丝插入比例（相对于螺丝长度，0.0-1.0）
    [Tooltip("螺丝最终位置的计算方式：\n- false: 使用插入比例（screwLength * screwInsertionRatio）\n- true: 使用固定插入深度（screwInsertionDepth）")]
    [SerializeField] private bool useFixedDepth = false;

    // 全局动画速率，1.0f为正常速度
    private float _animationSpeedRate = 1.0f;

    /// <summary>
    /// 动画速率属性，控制所有动画的播放速度
    /// </summary>
    public float AnimationSpeedRate
    {
        get { return _animationSpeedRate; }
        set { _animationSpeedRate = Mathf.Clamp(value, 0.1f, 5.0f); } // 限制速率范围
    }
    /**
     * 验证零件和挂载点是否有效
     *
     * @param part 要验证的零件
     * @param partName 零件名称（用于错误消息）
     * @return 是否有效
     */
    private bool ValidatePart(IAssemblyPart part, string partName = "")
    {
        string name = string.IsNullOrEmpty(partName) ? part?.PartName : partName;

        if (part?.PartTransform == null)
        {
            Debug.LogError($"零件无效: {name}");
            return false;
        }

        if (!part.HasReferencePoints)
        {
            Debug.LogError($"零件没有参考点: {name}");
            return false;
        }

        return true;
    }

    /**
     * 执行平滑动画
     *
     * @param duration 动画持续时间
     * @param updateAction 每帧更新的动作
     * @param completeAction 动画完成时的动作
     */
    private IEnumerator AnimateOverTime(
        float duration,
        System.Action<float> updateAction,
        System.Action completeAction = null
    )
    {
        float elapsed = 0f;

        // 应用速率调整后的持续时间
        float adjustedDuration = duration / _animationSpeedRate;

        while (elapsed < adjustedDuration)
        {
            // 使用调整后的时间增量
            elapsed += Time.deltaTime;
            float t = Mathf.SmoothStep(0f, 1f, elapsed / adjustedDuration);

            updateAction?.Invoke(t);

            yield return null;
        }

        // 确保最终状态准确
        updateAction?.Invoke(1.0f);
        completeAction?.Invoke();
    }

    /**
     * 通用零件移动方法
     *
     * @param part 要移动的零件
     * @param targetPosition 目标位置
     * @param targetRotation 目标旋转（可选）
     * @param duration 动画持续时间
     * @param useByMountPoint 是否以挂载点为基准移动
     * @param referencePointIndex 参考点索引（默认为0）
     */
    public IEnumerator MovePart(
        IAssemblyPart part,
        Vector3 targetPosition,
        Quaternion? targetRotation = null,
        float duration = 1.0f,
        bool useByMountPoint = true,
        int referencePointIndex = 0
    )
    {
        if (!ValidatePart(part))
        {
            yield break;
        }

        // 记录初始状态
        Vector3 startPos = part.PartTransform.position;
        Quaternion startRot = part.PartTransform.rotation;

        // 使用指定索引的参考点
        Transform referencePoint = part.GetReferencePoint(referencePointIndex);
        if (referencePoint == null)
        {
            Debug.LogError($"零件 {part.PartName} 没有索引为 {referencePointIndex} 的参考点！");
            yield break;
        }

        Vector3 startMountPointPos = referencePoint.position;

        // 计算物体到挂载点的本地偏移（在物体的本地坐标系中）
        Vector3 localOffset = part.PartTransform.InverseTransformPoint(referencePoint.position);

        #if UNITY_EDITOR
        // 记录初始状态用于调试
        Debug.Log($"移动开始 - 物体: {part.PartName}, 初始位置: {startPos}, 挂载点: {startMountPointPos}");
        Debug.Log($"  目标位置: {targetPosition}, 使用挂载点: {useByMountPoint}");
        #endif

        // 计算目标位置
        Vector3 targetPartPos;
        Vector3 targetMountPointPos;

        if (useByMountPoint) {
            // 如果使用挂载点为基准，则目标位置是挂载点的目标位置
            targetMountPointPos = targetPosition;
            // 物体位置需要保持与挂载点的相对关系
            targetPartPos = startPos; // 暂时使用起始位置，会在动画中更新
        } else {
            // 如果直接移动物体，则目标位置是物体的目标位置
            targetPartPos = targetPosition;
            // 挂载点会跟随物体移动
            targetMountPointPos = startMountPointPos; // 暂时使用起始位置，会在动画中更新
        }

        // 使用通用动画方法
        yield return AnimateOverTime(
            duration,
            (t) => {
                if (useByMountPoint) {
                    // 更新挂载点位置
                    Vector3 newMountPointPos = Vector3.Lerp(startMountPointPos, targetMountPointPos, t);

                    // 计算物体新位置，保持与挂载点的相对关系
                    Vector3 newPartPos = newMountPointPos - part.PartTransform.TransformVector(localOffset);

                    // 更新物体位置
                    part.PartTransform.position = newPartPos;
                } else {
                    // 直接更新物体位置
                    Vector3 newPartPos = Vector3.Lerp(startPos, targetPartPos, t);
                    part.PartTransform.position = newPartPos;
                }

                // 处理旋转
                if (targetRotation.HasValue) {
                    part.PartTransform.rotation = Quaternion.Slerp(startRot, targetRotation.Value, t);
                }
            },
            () => {
                // 确保最终位置和旋转准确
                if (useByMountPoint) {
                    // 设置最终挂载点位置
                    Vector3 finalMountPointPos = targetMountPointPos;

                    // 计算最终物体位置
                    Vector3 finalPartPos = finalMountPointPos - part.PartTransform.TransformVector(localOffset);
                    part.PartTransform.position = finalPartPos;

                    #if UNITY_EDITOR
                    // 验证挂载点位置
                    float positionError = Vector3.Distance(referencePoint.position, finalMountPointPos);
                    if (positionError > 0.0001f) {
                        Debug.LogWarning($"挂载点位置误差: {positionError}，进行修正");
                    }
                    #endif
                } else {
                    // 直接设置最终物体位置
                    part.PartTransform.position = targetPartPos;
                }

                // 设置最终旋转
                if (targetRotation.HasValue) {
                    part.PartTransform.rotation = targetRotation.Value;
                }

                #if UNITY_EDITOR
                Debug.Log($"移动完成 - 物体: {part.PartName}, 最终位置: {part.PartTransform.position}");
                Debug.Log($"  参考点[{referencePointIndex}]最终位置: {referencePoint.position}");
                #endif
            }
        );
    }

    /**
     * 通用零件原地旋转方法
     *
     * 保持挂载点位置不变，只改变物体的旋转
     *
     * @param part 要旋转的零件
     * @param targetRotation 目标旋转
     * @param duration 动画持续时间
     */
    public IEnumerator RotatePartInPlace(
        IAssemblyPart part,
        Quaternion targetRotation,
        float duration = 1.0f
    )
    {
        if (!ValidatePart(part))
        {
            yield break;
        }

        // 记录初始状态
        Quaternion startRot = part.PartTransform.rotation;
        Vector3 startPos = part.PartTransform.position;
        Vector3 mountPointStartPos = part.GetReferencePoint(0).position;

        // 计算物体到挂载点的本地偏移（在物体的本地坐标系中）
        Vector3 localOffset = part.PartTransform.InverseTransformPoint(part.GetReferencePoint(0).position);

        #if UNITY_EDITOR
        // 记录初始状态用于调试
        Debug.Log($"旋转开始 - 物体: {part.PartName}, 初始位置: {startPos}, 挂载点: {mountPointStartPos}");
        Debug.Log($"  目标旋转: {targetRotation.eulerAngles}, 本地偏移: {localOffset}");
        #endif

        // 使用通用动画方法
        yield return AnimateOverTime(
            duration,
            (t) => {
                // 应用旋转插值 - 使用球面线性插值确保平滑过渡
                Quaternion currentRotation = Quaternion.Slerp(startRot, targetRotation, t);
                part.PartTransform.rotation = currentRotation;

                // 保持挂载点在世界空间中的位置不变
                Vector3 worldMountPointPos = mountPointStartPos;

                // 将本地偏移转换为世界坐标中的位置
                Vector3 newPartPos = worldMountPointPos - part.PartTransform.TransformVector(localOffset);

                // 更新物体位置，保持挂载点位置不变
                part.PartTransform.position = newPartPos;
            },
            () => {
                // 确保最终旋转准确
                part.PartTransform.rotation = targetRotation;

                // 确保挂载点位置准确
                Vector3 finalWorldMountPointPos = mountPointStartPos;
                Vector3 finalPartPos = finalWorldMountPointPos - part.PartTransform.TransformVector(localOffset);
                part.PartTransform.position = finalPartPos;

                #if UNITY_EDITOR
                // 验证最终位置
                float positionError = Vector3.Distance(part.GetReferencePoint(0).position, mountPointStartPos);
                if (positionError > 0.0001f) {
                    Debug.LogWarning($"挂载点位置误差: {positionError}，进行修正");
                }

                Debug.Log($"旋转完成 - 物体: {part.PartName}, 最终位置: {part.PartTransform.position}");
                Debug.Log($"  参考点最终位置: {part.GetReferencePoint(0).position}, 误差: {positionError}");
                #endif
            }
        );
    }

    /**
     * 通用零件轴向旋转方法
     *
     * 绕指定轴旋转物体，可以指定旋转轴心点
     *
     * @param part 要旋转的零件
     * @param axis 旋转轴
     * @param angle 旋转角度
     * @param pivotPoint 旋转轴心点（可选，默认为挂载点）
     * @param duration 动画持续时间
     */
    public IEnumerator RotatePartAroundAxis(
        IAssemblyPart part,
        Vector3 axis,
        float angle,
        Vector3? pivotPoint = null,
        float duration = 1.0f
    )
    {
        if (!ValidatePart(part))
        {
            yield break;
        }

        Quaternion startRot = part.PartTransform.rotation;
        Vector3 startPos = part.PartTransform.position;
        Vector3 actualPivot = pivotPoint ?? part.GetReferencePoint(0).position;

        // 使用通用动画方法
        yield return AnimateOverTime(
            duration,
            (t) => {
                // 计算当前已旋转角度
                float currentAngle = angle * t;

                // 绕轴旋转
                Quaternion additionalRotation = Quaternion.AngleAxis(currentAngle, axis);
                part.PartTransform.rotation = startRot * additionalRotation;

                // 如果有轴心点，还需要修正位置
                if (pivotPoint.HasValue) {
                    Vector3 startPosRelativeToPivot = startPos - actualPivot;
                    Vector3 newPosRelativeToPivot = additionalRotation * startPosRelativeToPivot;
                    part.PartTransform.position = actualPivot + newPosRelativeToPivot;
                }
            },
            () => {
                // 确保最终旋转和位置准确
                Quaternion finalRotation = Quaternion.AngleAxis(angle, axis);
                part.PartTransform.rotation = startRot * finalRotation;

                if (pivotPoint.HasValue) {
                    Vector3 startPosRelativeToPivot = startPos - actualPivot;
                    Vector3 newPosRelativeToPivot = finalRotation * startPosRelativeToPivot;
                    part.PartTransform.position = actualPivot + newPosRelativeToPivot;
                }
            }
        );
    }

    /**
     * 以挂载点为基准移动和旋转物体
     *
     * @param part 要移动的零件
     * @param targetMountPointPos 挂载点的目标位置
     * @param targetRotation 目标旋转
     * @param duration 动画持续时间
     */
    public IEnumerator MoveAndRotateByMountPoint(
        IAssemblyPart part,
        Vector3 targetMountPointPos,
        Quaternion targetRotation,
        float duration = 1.0f
    )
    {
        if (!ValidatePart(part))
        {
            yield break;
        }

        // 记录初始状态
        Vector3 startMountPointPos = part.GetReferencePoint(0).position;
        Quaternion startRotation = part.PartTransform.rotation;

        // 计算需要应用的偏移
        Vector3 objectOffset = part.PartTransform.position - part.GetReferencePoint(0).position;

        // 使用通用动画方法
        yield return AnimateOverTime(
            duration,
            (t) => {
                // 1. 插值计算挂载点的新位置
                Vector3 newMountPointPos = Vector3.Lerp(startMountPointPos, targetMountPointPos, t);

                // 2. 设置挂载点位置
                part.GetReferencePoint(0).position = newMountPointPos;

                // 3. 应用旋转
                part.PartTransform.rotation = Quaternion.Slerp(startRotation, targetRotation, t);

                // 4. 更新物体位置以保持原有的偏移
                part.PartTransform.position = part.GetReferencePoint(0).position + part.PartTransform.rotation * Quaternion.Inverse(startRotation) * objectOffset;
            },
            () => {
                // 确保最终位置和旋转准确
                part.GetReferencePoint(0).position = targetMountPointPos;
                part.PartTransform.rotation = targetRotation;

                // 根据偏移计算最终物体位置
                part.PartTransform.position = part.GetReferencePoint(0).position + part.PartTransform.rotation * Quaternion.Inverse(startRotation) * objectOffset;

                #if UNITY_EDITOR
                Debug.Log($"MoveAndRotateByMountPoint完成 - 物体: {part.PartName}");
                Debug.Log($"  参考点最终位置: {part.GetReferencePoint(0).position}, 目标最终位置: {part.PartTransform.position}");
                #endif
            }
        );
    }

    /**
     * 仅移动挂载点，物体跟随
     *
     * @param part 要移动的零件
     * @param targetMountPointPos 挂载点的目标位置
     * @param duration 动画持续时间
     */
    public IEnumerator MoveByMountPoint(
        IAssemblyPart part,
        Vector3 targetMountPointPos,
        float duration = 1.0f
    )
    {
        if (!ValidatePart(part))
        {
            yield break;
        }

        // 记录初始状态
        Vector3 startMountPointPos = part.GetReferencePoint(0).position;

        // 计算初始偏移
        Vector3 initialOffset = part.PartTransform.position - part.GetReferencePoint(0).position;

        // 使用通用动画方法
        yield return AnimateOverTime(
            duration,
            (t) => {
                // 1. 插值计算挂载点的新位置
                Vector3 newMountPointPos = Vector3.Lerp(startMountPointPos, targetMountPointPos, t);

                // 2. 设置挂载点位置
                part.GetReferencePoint(0).position = newMountPointPos;

                // 3. 更新物体位置，保持相对挂载点的偏移
                part.PartTransform.position = part.GetReferencePoint(0).position + initialOffset;
            },
            () => {
                // 确保最终位置准确
                part.GetReferencePoint(0).position = targetMountPointPos;
                part.PartTransform.position = part.GetReferencePoint(0).position + initialOffset;

                #if UNITY_EDITOR
                Debug.Log($"MoveByMountPoint完成 - 物体: {part.PartName}");
                Debug.Log($"  参考点最终位置: {part.GetReferencePoint(0).position}, 目标最终位置: {part.PartTransform.position}");
                #endif
            }
        );
    }

    /**
     * 强制对齐变换位置
     *
     * @param transform 要对齐的变换
     * @param targetPosition 目标位置
     */
    public void AlignTransformPosition(Transform transform, Vector3 targetPosition)
    {
        if (transform == null) return;

        transform.position = targetPosition;

        #if UNITY_EDITOR
        Debug.Log($"强制对齐 {transform.name} 到位置 {targetPosition}");
        #endif
    }

    /**
     * 对齐两个零件的参考点
     *
     * @param movingPart 要移动的零件
     * @param targetPart 目标零件
     * @param movingPartRefIndex 移动零件的参考点索引
     * @param targetPartRefIndex 目标零件的参考点索引
     * @param duration 动画持续时间
     */
    public IEnumerator AlignParts(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        int movingPartRefIndex ,
        int targetPartRefIndex ,
        float duration = 1.0f
    )
    {
        if (!ValidatePart(movingPart) || !ValidatePart(targetPart))
        {
            yield break;
        }

        Transform movingRef = movingPart.GetReferencePoint(movingPartRefIndex);
        Transform targetRef = targetPart.GetReferencePoint(targetPartRefIndex);


        if (movingRef == null || targetRef == null)
        {
            Debug.LogError($"无法对齐：参考点未设置。移动零件: {movingPart.PartName}, 目标零件: {targetPart.PartName}");
            yield break;
        }


        // 计算参考点相对于零件的本地位置和旋转
        Vector3 movingRefLocalPos = movingPart.PartTransform.InverseTransformPoint(movingRef.position);
        Vector3 targetRefLocalPos = targetPart.PartTransform.InverseTransformPoint(targetRef.position);

        // 计算目标旋转
        Quaternion targetRot = CalculateAlignmentRotation(targetRef, movingRef, movingPart);

        // 先旋转移动零件
        yield return RotatePartInPlace(movingPart, targetRot, duration * 0.5f);

        // 再移动到目标位置，使用指定的参考点索引
        yield return MovePart(movingPart, targetRef.position, null, duration * 0.5f, true, movingPartRefIndex);

        // 验证对齐精度
        float positionError = Vector3.Distance(movingRef.position, targetRef.position);
        float rotationError = Quaternion.Angle(movingRef.rotation, targetRef.rotation);
        // 如果有多个参考点，检查其他参考点的对齐情况
        if (movingPart.ReferencePointCount > 1 && targetPart.ReferencePointCount > 1)
        {
            int minPoints = Mathf.Min(movingPart.ReferencePointCount, targetPart.ReferencePointCount);

            for (int i = 0; i < minPoints; i++)
            {
                if (i == movingPartRefIndex) continue; // 跳过已对齐的参考点

                Transform otherMovingRef = movingPart.GetReferencePoint(i);
                Transform otherTargetRef = targetPart.GetReferencePoint(i);

                if (otherMovingRef != null && otherTargetRef != null)
                {
                    float otherPosError = Vector3.Distance(otherMovingRef.position, otherTargetRef.position);
                    float otherRotError = Quaternion.Angle(otherMovingRef.rotation, otherTargetRef.rotation);

                    Debug.Log($"参考点 {i} - 位置误差: {otherPosError}, 旋转误差: {otherRotError}°");
                }
            }
        }
    }

    /**
     * 计算对齐旋转
     *
     * 计算将movingRef对齐到targetRef所需的旋转
     */
    private Quaternion CalculateAlignmentRotation(Transform targetRef, Transform movingRef, AssemblyPart movingPart)
    {
        if (targetRef == null || movingRef == null)
        {
            Debug.LogError("无法计算目标旋转：参考点未设置");
            return movingPart.PartTransform.rotation; // 返回当前旋转，避免错误
        }

        // 获取两个参考点的当前旋转
        Quaternion currentRefRot = movingRef.rotation;
        Quaternion targetRefRot = targetRef.rotation;

        // 计算从movingRef到targetRef的旋转差异
        Quaternion deltaRot = targetRefRot * Quaternion.Inverse(currentRefRot);

        // 记录旋转差异
        Debug.Log($"旋转差异 (deltaRot): {deltaRot.eulerAngles}");

        // 记录零件当前旋转
        Debug.Log($"零件当前旋转: {movingPart.PartTransform.rotation.eulerAngles}");

        // 应用这个旋转差异到movingPart的当前旋转
        Quaternion finalRotation = deltaRot * movingPart.PartTransform.rotation;

        // 记录最终计算的旋转
        Debug.Log($"计算得到的最终旋转: {finalRotation.eulerAngles}");
        Debug.Log("=== 旋转计算结束 ===");

        return finalRotation;
    }

    // ==================== 螺丝螺母装配动画方法 ====================

    /// <summary>
    /// 获取指定轴的方向向量
    /// </summary>
    private Vector3 GetAxisDirection(Transform axisTransform)
    {
        if (axisTransform == null)
            return Vector3.forward;

        // 使用前方向作为轴向
        return axisTransform.forward;
    }

    /// <summary>
    /// 计算螺丝的最终位置
    /// </summary>
    /// <param name="holePosition">孔位位置</param>
    /// <param name="holeDirection">孔位方向（螺丝插入方向）</param>
    /// <param name="customDepth">自定义插入深度（可选）</param>
    /// <returns>螺丝最终位置</returns>
    private Vector3 CalculateScrewFinalPosition(Vector3 holePosition, Vector3 holeDirection, float? customDepth = null)
    {
        float insertionDistance;

        if (customDepth.HasValue)
        {
            // 使用传入的自定义深度
            insertionDistance = customDepth.Value;
            Debug.Log($" 使用自定义插入深度: {insertionDistance:F4}m");
        }
        else if (useFixedDepth)
        {
            // 使用固定插入深度
            insertionDistance = screwInsertionDepth;
            Debug.Log($"使用固定插入深度: {insertionDistance:F4}m");
        }
        else
        {
            // 使用插入比例
            insertionDistance = screwLength * screwInsertionRatio;
            Debug.Log($"使用插入比例: {screwInsertionRatio:F2} × {screwLength:F4}m = {insertionDistance:F4}m");
        }

        Vector3 finalPosition = holePosition + holeDirection * insertionDistance;
        Debug.Log($" 螺丝最终位置: {finalPosition} (从孔位 {holePosition} 沿方向 {holeDirection} 移动 {insertionDistance:F4}m)");

        return finalPosition;
    }

    /// <summary>
    /// 设置螺丝插入深度（运行时调用）
    /// </summary>
    /// <param name="depth">插入深度</param>
    public void SetScrewInsertionDepth(float depth)
    {
        screwInsertionDepth = Mathf.Max(0, depth);
        useFixedDepth = true;
        Debug.Log($"🔧设置螺丝插入深度: {screwInsertionDepth:F4}m");
    }

    /// <summary>
    /// 设置螺丝插入比例（运行时调用）
    /// </summary>
    /// <param name="ratio">插入比例（0.0-1.0）</param>
    public void SetScrewInsertionRatio(float ratio)
    {
        screwInsertionRatio = Mathf.Clamp01(ratio);
        useFixedDepth = false;
        Debug.Log($"🔧 设置螺丝插入比例: {screwInsertionRatio:F2}");
    }

    /// <summary>
    /// 螺丝安装动画
    /// </summary>
    /// <param name="screwPart">螺丝零件</param>
    /// <param name="holeRef">孔位参考点</param>
    /// <param name="holeDirection">孔位方向（螺丝插入方向）</param>
    /// <param name="duration">动画持续时间</param>
    /// <param name="customInsertionDepth">自定义插入深度（可选）</param>
    public IEnumerator InstallScrew(
        AssemblyPart screwPart,
        Transform holeRef,
        Vector3 holeDirection,
        float duration = 0f,
        float? customInsertionDepth = null
    )
    {
        if (screwPart == null || holeRef == null)
        {
            Debug.LogError($"螺丝或孔位无效");
            yield break;
        }

        float actualDuration = duration > 0 ? duration : animationDuration;
        Debug.Log($"孔位位置: {holeRef.position}, 方向: {holeDirection}");

        // 计算螺丝的起始位置和最终位置
        Vector3 screwStartPos = holeRef.position - holeDirection * initialDistanceFromHole + Vector3.up * screwInitialElevation;
        Vector3 screwEndPos = CalculateScrewFinalPosition(holeRef.position, holeDirection, customInsertionDepth);

        // 1. 移动螺丝到起始位置
        yield return MovePart(screwPart, screwStartPos, Quaternion.LookRotation(-holeDirection), actualDuration * 0.3f, true);

        // 2. 移动螺丝到最终位置（穿过孔位并停在指定深度）
        yield return MovePart(screwPart, screwEndPos, null, actualDuration * 0.3f, true);

        // 3. 旋转螺丝（模拟拧紧）
        Quaternion currentRot = screwPart.PartTransform.rotation;
        Quaternion targetRot = currentRot * Quaternion.AngleAxis(720f, holeDirection);
        yield return RotatePartInPlace(screwPart, targetRot, actualDuration * 0.4f);
    }

    /// <summary>
    /// 螺丝安装动画（重载方法，支持指定插入深度）
    /// </summary>
    /// <param name="screwPart">螺丝零件</param>
    /// <param name="holeRef">孔位参考点</param>
    /// <param name="holeDirection">孔位方向</param>
    /// <param name="insertionDepth">插入深度</param>
    /// <param name="duration">动画持续时间</param>
    public IEnumerator InstallScrewWithDepth(
        AssemblyPart screwPart,
        Transform holeRef,
        Vector3 holeDirection,
        float insertionDepth,
        float duration = 0f
    )
    {
        return InstallScrew(screwPart, holeRef, holeDirection, duration, insertionDepth);
    }

    /// <summary>
    /// 螺母安装动画
    /// </summary>
    public IEnumerator InstallNut(
        AssemblyPart nutPart,
        Transform holeRef,
        Vector3 holeDirection,
        float duration = 0f
    )
    {
        if (nutPart == null || holeRef == null)
        {
            Debug.LogError($"螺母或孔位无效");
            yield break;
        }

        float actualDuration = duration > 0 ? duration : animationDuration;

        // 计算螺丝末端位置（螺母的目标位置）- 使用与螺丝相同的插入深度计算
        Vector3 screwEndPosition = CalculateScrewFinalPosition(holeRef.position, holeDirection);

        // 计算螺母的起始位置（在螺丝末端上方）
        Vector3 nutStartPos = screwEndPosition + Vector3.up * 0.03f + holeDirection * (initialDistanceFromHole * 0.3f);

        // 计算螺母的最终位置（拧紧后的位置）
        Vector3 nutEndPos = screwEndPosition - holeDirection * (nutThickness * 0.7f);

        // 1. 移动螺母到起始位置
        yield return MovePart(nutPart, nutStartPos, Quaternion.LookRotation(-holeDirection), actualDuration * 0.3f, true);

        // 2. 移动螺母到螺丝末端
        yield return MovePart(nutPart, screwEndPosition, null, actualDuration * 0.3f, true);

        // 3. 旋转螺母（模拟拧紧）
        Quaternion currentNutRot = nutPart.PartTransform.rotation;
        Quaternion targetNutRot = currentNutRot * Quaternion.AngleAxis(720f, holeDirection);
        yield return RotatePartInPlace(nutPart, targetNutRot, actualDuration * 0.3f);

        // 4. 移动螺母到最终位置（拧紧后的位置）
        yield return MovePart(nutPart, nutEndPos, null, actualDuration * 0.1f, true);
    }

    /// <summary>
    /// 便捷方法：对齐零件（使用默认持续时间）
    /// </summary>
    public void AlignPartsAsync(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        int movingPartRefIndex,
        int targetPartRefIndex 
    )
    {
        StartCoroutine(AlignParts(movingPart, targetPart, movingPartRefIndex, targetPartRefIndex, animationDuration));
    }

    /// <summary>
    /// 同步方法：对齐零件（返回协程，可以被等待）
    /// </summary>
    public IEnumerator AlignPartsSync(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        int movingPartRefIndex ,
        int targetPartRefIndex ,
        float duration
    )
    {
        float actualDuration = duration > 0 ? duration : animationDuration;
        yield return AlignParts(movingPart, targetPart, movingPartRefIndex, targetPartRefIndex, actualDuration);
    }

    /// <summary>
    /// 完整的装配动画序列：对齐 + 螺丝 + 螺母
    /// 确保所有动画使用相同的基础时长，保证速度一致
    /// </summary>
    public IEnumerator CompleteAssemblySequence(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        AssemblyPart screwPart,
        AssemblyPart nutPart,
        Transform holeRef,
        Vector3 holeDirection,
        int movingPartRefIndex = 0,
        int targetPartRefIndex = 0,
        float baseDuration = 0f
    )
    {
        float actualDuration = baseDuration > 0 ? baseDuration : animationDuration;

        Debug.Log($"开始完整装配序列，基础时长: {actualDuration}秒");

        // 1. 对齐零件
        Debug.Log("步骤1: 对齐零件");
        yield return AlignParts(movingPart, targetPart, movingPartRefIndex, targetPartRefIndex, actualDuration);

        // 2. 安装螺丝
        if (screwPart != null)
        {
            Debug.Log("步骤2: 安装螺丝");
            yield return InstallScrew(screwPart, holeRef, holeDirection, actualDuration);
        }

        // 3. 安装螺母
        if (nutPart != null)
        {
            Debug.Log("步骤3: 安装螺母");
            yield return InstallNut(nutPart, holeRef, holeDirection, actualDuration);
        }

        Debug.Log("完整装配序列完成");
    }
}
