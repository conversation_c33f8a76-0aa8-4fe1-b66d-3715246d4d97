Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 32609 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/xjj/VRasmb0722
-logFile
Logs/AssetImportWorker1.log
-srvPort
49428
Successfully changed project path to: D:/xjj/VRasmb0722
D:/xjj/VRasmb0722
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32708] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3085228394 [EditorId] 3085228394 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [32708] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3085228394 [EditorId] 3085228394 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 34.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path D:/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/xjj/VRasmb0722/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 (ID=0x2786)
    Vendor:   NVIDIA
    VRAM:     12012 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56892
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001340 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1520 ms
Refreshing native plugins compatible for Editor in 34.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.889 seconds
Domain Reload Profiling:
	ReloadAssembly (1889ms)
		BeginReloadAssembly (53ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1783ms)
			LoadAssemblies (52ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (64ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (1679ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1564ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (34ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (55ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.003707 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 34.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.201 seconds
Domain Reload Profiling:
	ReloadAssembly (1202ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (12ms)
		EndReloadAssembly (1061ms)
			LoadAssemblies (61ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (178ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (769ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (35ms)
				BeforeProcessingInitializeOnLoad (38ms)
				ProcessInitializeOnLoadAttributes (672ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.15 seconds
Refreshing native plugins compatible for Editor in 0.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4461 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (119.9 KB). Loaded Objects now: 4942.
Memory consumption went from 181.0 MB to 180.9 MB.
Total: 2.205900 ms (FindLiveObjects: 0.181800 ms CreateObjectMapping: 0.052200 ms MarkObjects: 1.904600 ms  DeleteObjects: 0.066400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 687324.947403 seconds.
  path: Assets/Prefabs/10scale/LDF06电机.prefab
  artifactKey: Guid(4ac475157a45f4846b038087362e7747) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/LDF06电机.prefab using Guid(4ac475157a45f4846b038087362e7747) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '83af04d07f0ac1c55dc5ada539f257b8') in 0.137342 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.037287 seconds.
  path: Assets/Prefabs/10scale/M3X16-16.prefab
  artifactKey: Guid(d9f78e63c51e5ca4fabfe1aca407a268) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/M3X16-16.prefab using Guid(d9f78e63c51e5ca4fabfe1aca407a268) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '65d5ba51d294f692324e60a4c85d47b1') in 0.017604 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.283851 seconds.
  path: Assets/Prefabs/10scale/nut m3-c.prefab
  artifactKey: Guid(9163adcc43c16924493f0c1e71d15909) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/nut m3-c.prefab using Guid(9163adcc43c16924493f0c1e71d15909) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '35548ba96b7bf1bd37ff07c46953b53e') in 0.017210 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.175592 seconds.
  path: Assets/Prefabs/10scale/主舵盘.prefab
  artifactKey: Guid(00deee7e2f5b5d948932a36bccbb452b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/主舵盘.prefab using Guid(00deee7e2f5b5d948932a36bccbb452b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f07de7a9d3ed11babdb807cb24368caa') in 0.016149 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.201024 seconds.
  path: Assets/Prefabs/10scale/小连杆反3.prefab
  artifactKey: Guid(deaaf00a8084f124995b74424eaf0881) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/小连杆反3.prefab using Guid(deaaf00a8084f124995b74424eaf0881) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c24ca2e2ec6d16226b4faeb5735dccde') in 0.013937 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.206614 seconds.
  path: Assets/Prefabs/10scale/抓反1.prefab
  artifactKey: Guid(1eee6ff268a7f5e4b93e9112038ec4ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/抓反1.prefab using Guid(1eee6ff268a7f5e4b93e9112038ec4ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '37944d3e0b22372b9a4a1b075d20e7eb') in 0.016564 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Prefabs/10scale/抓反2.prefab
  artifactKey: Guid(ed1d833cc4b17cb458c30259f4f387ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/抓反2.prefab using Guid(ed1d833cc4b17cb458c30259f4f387ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f1ccaff178a3f9865fc6fde6edc26fa2') in 0.022091 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Prefabs/10scale/抓反3.prefab
  artifactKey: Guid(37e11fa895612c647be63f95f1a39393) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/抓反3.prefab using Guid(37e11fa895612c647be63f95f1a39393) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9f62be32a683bbeb1361803dc4872dfe') in 0.016388 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Prefabs/10scale/螺孔小圆柱反1.prefab
  artifactKey: Guid(06280c3f6adbada4498ed1305501b6de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/螺孔小圆柱反1.prefab using Guid(06280c3f6adbada4498ed1305501b6de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '349b1536cf82211fdce9619e06b0ee46') in 0.018065 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Prefabs/10scale/安装连接块反1.prefab
  artifactKey: Guid(db90da74e651245429815d28edcee4be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/安装连接块反1.prefab using Guid(db90da74e651245429815d28edcee4be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '79c5538d2f9cf1f1da3994b478ea745c') in 0.026999 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Prefabs/10scale/中下段.prefab
  artifactKey: Guid(62c9bc1532340a54cbbe67a67a41e279) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/中下段.prefab using Guid(62c9bc1532340a54cbbe67a67a41e279) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '936035c402f5db5efbd10d4245304b61') in 0.030103 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Prefabs/10scale/安装连接块反2.prefab
  artifactKey: Guid(ba9cd10da6e37384f93d547f8a27748b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/安装连接块反2.prefab using Guid(ba9cd10da6e37384f93d547f8a27748b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4779bf89478a1d0864e5820f2c6ad7ff') in 0.023972 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Prefabs/10scale/小圆柱反1.prefab
  artifactKey: Guid(eb4ac4f5676f18a40959466ad19530ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/小圆柱反1.prefab using Guid(eb4ac4f5676f18a40959466ad19530ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '008250d03327f67e2f7204812e1d8e6d') in 0.022950 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005327 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.473 seconds
Domain Reload Profiling:
	ReloadAssembly (1473ms)
		BeginReloadAssembly (128ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (1281ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (270ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (851ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (40ms)
				ProcessInitializeOnLoadAttributes (781ms)
				ProcessInitializeOnLoadMethodAttributes (18ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4440 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (92.6 KB). Loaded Objects now: 4968.
Memory consumption went from 183.5 MB to 183.4 MB.
Total: 3.848600 ms (FindLiveObjects: 0.468600 ms CreateObjectMapping: 0.128300 ms MarkObjects: 3.183900 ms  DeleteObjects: 0.065400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0