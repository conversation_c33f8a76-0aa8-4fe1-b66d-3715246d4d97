Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 32609 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
D:/xjj/VRasmb0722
-logFile
Logs/AssetImportWorker3.log
-srvPort
49428
Successfully changed project path to: D:/xjj/VRasmb0722
D:/xjj/VRasmb0722
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [27564] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2300495005 [EditorId] 2300495005 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [27564] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2300495005 [EditorId] 2300495005 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 50.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path D:/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/xjj/VRasmb0722/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 (ID=0x2786)
    Vendor:   NVIDIA
    VRAM:     12012 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56756
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001383 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2871 ms
Refreshing native plugins compatible for Editor in 37.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  3.267 seconds
Domain Reload Profiling:
	ReloadAssembly (3268ms)
		BeginReloadAssembly (56ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (3157ms)
			LoadAssemblies (55ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (69ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (3044ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2920ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (37ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (56ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.003879 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 36.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.212 seconds
Domain Reload Profiling:
	ReloadAssembly (1213ms)
		BeginReloadAssembly (80ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (11ms)
		EndReloadAssembly (1069ms)
			LoadAssemblies (67ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (189ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (757ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (37ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (657ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.28 seconds
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4461 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (119.6 KB). Loaded Objects now: 4942.
Memory consumption went from 181.1 MB to 181.0 MB.
Total: 2.498900 ms (FindLiveObjects: 0.204500 ms CreateObjectMapping: 0.059200 ms MarkObjects: 2.160900 ms  DeleteObjects: 0.073200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006117 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.451 seconds
Domain Reload Profiling:
	ReloadAssembly (1452ms)
		BeginReloadAssembly (126ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (1247ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (244ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (42ms)
			SetupLoadedEditorAssemblies (845ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (45ms)
				ProcessInitializeOnLoadAttributes (767ms)
				ProcessInitializeOnLoadMethodAttributes (19ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4440 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (92.6 KB). Loaded Objects now: 4958.
Memory consumption went from 179.2 MB to 179.1 MB.
Total: 4.370900 ms (FindLiveObjects: 0.408100 ms CreateObjectMapping: 0.202200 ms MarkObjects: 3.665300 ms  DeleteObjects: 0.093800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0