Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.44f1c1 (0c301d0bd4e3) revision 798749'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 32609 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\2021.3.44f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/xjj/VRasmb0722
-logFile
Logs/AssetImportWorker0.log
-srvPort
49428
Successfully changed project path to: D:/xjj/VRasmb0722
D:/xjj/VRasmb0722
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [28472] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1825824454 [EditorId] 1825824454 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [28472] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1825824454 [EditorId] 1825824454 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-5O0E4G8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 34.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.44f1c1 (0c301d0bd4e3)
[Subsystems] Discovering subsystems at path D:/2021.3.44f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/xjj/VRasmb0722/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 (ID=0x2786)
    Vendor:   NVIDIA
    VRAM:     12012 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/2021.3.44f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/2021.3.44f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56660
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/2021.3.44f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001312 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1408 ms
Refreshing native plugins compatible for Editor in 35.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.778 seconds
Domain Reload Profiling:
	ReloadAssembly (1778ms)
		BeginReloadAssembly (53ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (1671ms)
			LoadAssemblies (53ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (63ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (16ms)
			SetupLoadedEditorAssemblies (1568ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1449ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (36ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (56ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.003573 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 36.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.224 seconds
Domain Reload Profiling:
	ReloadAssembly (1224ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (11ms)
		EndReloadAssembly (1087ms)
			LoadAssemblies (61ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (183ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (786ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (37ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (687ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 0.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4461 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (119.7 KB). Loaded Objects now: 4942.
Memory consumption went from 181.1 MB to 181.0 MB.
Total: 2.211400 ms (FindLiveObjects: 0.163600 ms CreateObjectMapping: 0.055100 ms MarkObjects: 1.930900 ms  DeleteObjects: 0.061300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 686826.608991 seconds.
  path: Assets/Scripts/AssemblyAnimationManager.cs
  artifactKey: Guid(e699600b241a91d418ad7694c4b3fcbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scripts/AssemblyAnimationManager.cs using Guid(e699600b241a91d418ad7694c4b3fcbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e098357ff2303442616fdc66a88c639e') in 0.037862 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 498.298376 seconds.
  path: Assets/Prefabs/10scale/BA_5-1.prefab
  artifactKey: Guid(c9687b21785807946aeae8ca1e602f62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/BA_5-1.prefab using Guid(c9687b21785807946aeae8ca1e602f62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a5595fe51fa9443258c0d7456bbf0a90') in 0.070625 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.104675 seconds.
  path: Assets/Prefabs/10scale/LDX335电机.prefab
  artifactKey: Guid(9ea0b622de9300e4a9c6828e2b7d581b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/LDX335电机.prefab using Guid(9ea0b622de9300e4a9c6828e2b7d581b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a4247ac681e834bbfd5144bf67676020') in 0.021773 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.279878 seconds.
  path: Assets/Prefabs/10scale/ST2.9X6.5.prefab
  artifactKey: Guid(7e494172b24f54a4dbafc72eef6ecafa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/ST2.9X6.5.prefab using Guid(7e494172b24f54a4dbafc72eef6ecafa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dc3102f4c52a907984d962b455bfc0e8') in 0.012586 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.180736 seconds.
  path: Assets/Prefabs/10scale/主机架反.prefab
  artifactKey: Guid(791a8e2787b256f4299363bf4649e2a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/主机架反.prefab using Guid(791a8e2787b256f4299363bf4649e2a8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bccf8c21332706a8256578f5f535b5a') in 0.016714 seconds 
Number of asset objects unloaded after import = 70
========================================================================
Received Import Request.
  Time since last request: 0.199699 seconds.
  path: Assets/Prefabs/10scale/小连杆反2.prefab
  artifactKey: Guid(545ee6e58f1911f498679e641f974072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/小连杆反2.prefab using Guid(545ee6e58f1911f498679e641f974072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '77cee22057ef31418fdb375a25cd1cd7') in 0.013413 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.207994 seconds.
  path: Assets/Prefabs/10scale/小连杆反4.prefab
  artifactKey: Guid(810a1c04425eb424d9d37d9caa486cc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/小连杆反4.prefab using Guid(810a1c04425eb424d9d37d9caa486cc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '158401fd355e1cbb9fe463b7fe826900') in 0.015336 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Prefabs/10scale/抓反4.prefab
  artifactKey: Guid(602bebd0ebbf93a46b0a9d2860d556c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/抓反4.prefab using Guid(602bebd0ebbf93a46b0a9d2860d556c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5125c073b466580095d1318d7c38a584') in 0.020625 seconds 
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Prefabs/10scale/摇杆小孔反.prefab
  artifactKey: Guid(b2c2a2aa39d96d8448877af41cd3a92f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/摇杆小孔反.prefab using Guid(b2c2a2aa39d96d8448877af41cd3a92f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a3cd721bc73731f9528415fdb93115da') in 0.017944 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Prefabs/10scale/摇杆大孔反.prefab
  artifactKey: Guid(b8d748b26de8d114c882004a5993bc53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/摇杆大孔反.prefab using Guid(b8d748b26de8d114c882004a5993bc53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5ff7ecb777aaa6dcd5fefc7ba09708ac') in 0.046503 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Prefabs/10scale/M3X10-10.prefab
  artifactKey: Guid(712d5925225c937448f5e9d73f5d23e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/M3X10-10.prefab using Guid(712d5925225c937448f5e9d73f5d23e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4db6d1d8ebbb07d1d1283b87046091be') in 0.029991 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Prefabs/10scale/Nut.prefab
  artifactKey: Guid(acbc1155e4a1f52408b227d918ef56cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/Nut.prefab using Guid(acbc1155e4a1f52408b227d918ef56cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '703b4f1853ff5fcb9c2f1ccfb138b31c') in 0.020553 seconds 
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Prefabs/10scale/小连杆反1.prefab
  artifactKey: Guid(4e451af37a50c1c459e8c52c39f08ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Prefabs/10scale/小连杆反1.prefab using Guid(4e451af37a50c1c459e8c52c39f08ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2b5ee4e5f39b4f3c79a35d1b18c0d8c7') in 0.026436 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005443 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.437 seconds
Domain Reload Profiling:
	ReloadAssembly (1438ms)
		BeginReloadAssembly (132ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (2ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (1241ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (258ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (829ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (765ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4440 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (92.6 KB). Loaded Objects now: 4968.
Memory consumption went from 183.6 MB to 183.6 MB.
Total: 5.067700 ms (FindLiveObjects: 0.544000 ms CreateObjectMapping: 0.261500 ms MarkObjects: 4.159900 ms  DeleteObjects: 0.100000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0