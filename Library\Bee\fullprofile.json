{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 0, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 0, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 0, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 0, "tid": 1, "ts": 1753867498259361, "dur": 448536235, "ph": "X", "name": "Build ScriptAssemblies", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867498260217, "dur": 35878, "ph": "X", "name": "WriteBuildProgramData", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867498296241, "dur": 448489524, "ph": "X", "name": "<PERSON>", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867946785776, "dur": 9819, "ph": "X", "name": "Finish", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867946796903, "dur": 736, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867946799704, "dur": 667, "ph": "X", "name": "", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753867498448829, "dur": 321, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867498449156, "dur": 1135, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "RemoveStaleOutputs"}}, {"pid": 12345, "tid": 0, "ts": 1753867498450334, "dur": 29, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753867498450363, "dur": 725, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "BuildQueueInit"}}, {"pid": 12345, "tid": 0, "ts": 1753867498451105, "dur": 1320, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867498452425, "dur": 5, "ph": "X", "name": "SortWorkingStack", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767647, "dur": 139, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767786, "dur": 36, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767822, "dur": 32, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767854, "dur": 18, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767873, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767875, "dur": 28, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767903, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767905, "dur": 19, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767924, "dur": 36, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767960, "dur": 11, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767971, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767973, "dur": 16, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767989, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946767990, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768005, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768007, "dur": 15, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768022, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768024, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768036, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768037, "dur": 11, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768048, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768049, "dur": 10, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768059, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768061, "dur": 11, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768072, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768073, "dur": 7, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768080, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768081, "dur": 9, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768090, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768091, "dur": 9, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768100, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768102, "dur": 14, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768116, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768118, "dur": 7, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768125, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768127, "dur": 9, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768136, "dur": 2, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768138, "dur": 12, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768150, "dur": 1, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768151, "dur": 9, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768160, "dur": 0, "ph": "X", "name": "SharedResourceDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768846, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946768905, "dur": 1051, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753867498451014, "dur": 1421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867498452439, "dur": 602, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867498453045, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753867498453054, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867498453177, "dur": 254, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753867498453433, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753867498454030, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867498454031, "dur": 733, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753867498454765, "dur": 39, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E7315D8C3D4CDCDF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867498454805, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867498454807, "dur": 40808903, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E7315D8C3D4CDCDF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867539263724, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsNativeModule.dll_DDE87668934B49B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867539263730, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867539263733, "dur": 40351990, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsNativeModule.dll_DDE87668934B49B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867579615739, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_039205B315E660FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867579615743, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867579615748, "dur": 40620312, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_039205B315E660FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867620236074, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_CDE2A3FD05444D89.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867620236078, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867620236082, "dur": 40354995, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_CDE2A3FD05444D89.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867660591107, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_C9B5DE92199F9357.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867660591115, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867660591133, "dur": 41071152, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_C9B5DE92199F9357.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867701662304, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753867701662665, "dur": 22, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867701662691, "dur": 40835812, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753867742499307, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753867742499311, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867742499314, "dur": 131, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.AdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753867742499446, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.Management.ref.dll_404BC0C35449934F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867742499482, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867742499484, "dur": 40867166, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.Management.ref.dll_404BC0C35449934F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867783366661, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867783366742, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867783366746, "dur": 40096949, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867823463707, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867823463786, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867823463790, "dur": 40088320, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867863552119, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867863552284, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867863552287, "dur": 40698148, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867904250445, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867904250605, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753867904250609, "dur": 40580067, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753867944830689, "dur": 1936951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867498451230, "dur": 2155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867498453391, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867498453394, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867498453401, "dur": 1130, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867498454532, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A1CBBE30F2EE4016.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867498454754, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867498454758, "dur": 40130935, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A1CBBE30F2EE4016.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867538585707, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_C625EA7218B770AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867538585712, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867538585716, "dur": 40366007, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_C625EA7218B770AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867578951737, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UNETModule.dll_CDEDF1BBA87F089F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867578951741, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867578951745, "dur": 40747001, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UNETModule.dll_CDEDF1BBA87F089F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867619698761, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B19684FAECA6918F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867619698765, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867619698769, "dur": 40333809, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B19684FAECA6918F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867660032594, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_A327CB81156EE9CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867660032599, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867660032603, "dur": 40558761, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_A327CB81156EE9CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867700591390, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867700591398, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867700591402, "dur": 132, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867700591535, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753867700591702, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867700591705, "dur": 119, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753867700591825, "dur": 40, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753867700592150, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867700592152, "dur": 139, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753867700592291, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867700592293, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867700592295, "dur": 76, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.AdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753867700592371, "dur": 7930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867700600458, "dur": 36, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867700600498, "dur": 40520433, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867741121740, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867741122544, "dur": 42, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867741122590, "dur": 40764932, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867781888593, "dur": 18838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867781907679, "dur": 37, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867781907720, "dur": 40468692, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753867822377358, "dur": 20183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.CoreUtils.Editor.ref.dll_ABFC049C4594FF75.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867822397547, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867822397551, "dur": 40209797, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.CoreUtils.Editor.ref.dll_ABFC049C4594FF75.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867862607362, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867862607686, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867862607691, "dur": 40648091, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867903255800, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867903256217, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867903256225, "dur": 40532071, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867943788306, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867943788455, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753867943788459, "dur": 2759717, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753867946548184, "dur": 219504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867498451085, "dur": 1359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867498452447, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867498453401, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867498453567, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867498453909, "dur": 2100, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867498456010, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_E665CFD9ECCBFDCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867498456121, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867498456138, "dur": 40781981, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_E665CFD9ECCBFDCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867539238138, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1826D67FB36AA7FB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867539238143, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867539238147, "dur": 40184724, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1826D67FB36AA7FB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867579422887, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3001CB35A923187D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867579422892, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867579422895, "dur": 40701013, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3001CB35A923187D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867620123923, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_16B40157D22CA319.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867620123927, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867620123931, "dur": 41112225, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_16B40157D22CA319.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867661236188, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867661236199, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867661236205, "dur": 40266606, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867701502824, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701502976, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701502980, "dur": 190, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503171, "dur": 30, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503476, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701503479, "dur": 128, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503608, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503610, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701503613, "dur": 72, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503686, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503687, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701503688, "dur": 61, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503752, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701503976, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701503978, "dur": 129, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753867701504109, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753867701504748, "dur": 33, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867701504785, "dur": 40842386, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753867742348174, "dur": 21313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753867742369670, "dur": 52, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867742369726, "dur": 40791464, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753867783162307, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867783162316, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867783162320, "dur": 40693809, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867823856139, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867823856218, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867823856221, "dur": 40700022, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867864556252, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867864556413, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867864556417, "dur": 40313280, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867904869707, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867904869899, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753867904869902, "dur": 40783721, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753867945653632, "dur": 1114029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498451075, "dur": 1364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498452442, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498452544, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498452666, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498452750, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498452829, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498453146, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867498453716, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498453718, "dur": 597, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867498454316, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F0B835E846E8A60C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867498454411, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867498454413, "dur": 40647773, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F0B835E846E8A60C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867539102200, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_15964F958E99E3B6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867539102204, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867539102209, "dur": 40837982, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_15964F958E99E3B6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867579940208, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_88426F71C2053A6E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867579940214, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867579940218, "dur": 40808353, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_88426F71C2053A6E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867620748594, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_18B8C5C073C4A91D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867620748613, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867620748617, "dur": 40892690, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_18B8C5C073C4A91D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867661641325, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_1311CDE4F848A58C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867661641330, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867661641334, "dur": 40263999, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_1311CDE4F848A58C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905354, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905358, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701905362, "dur": 180, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905543, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905681, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701905684, "dur": 134, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905818, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905821, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701905823, "dur": 72, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905896, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905898, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701905900, "dur": 65, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701905966, "dur": 22, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906132, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906134, "dur": 113, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906247, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906250, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906252, "dur": 61, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906314, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906317, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906319, "dur": 62, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906382, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906386, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906387, "dur": 57, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906445, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906446, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906447, "dur": 53, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906501, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906730, "dur": 3, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906734, "dur": 96, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906830, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701906963, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701906965, "dur": 93, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907060, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907208, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907210, "dur": 91, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907302, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907303, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907305, "dur": 65, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907371, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907534, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907536, "dur": 98, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907635, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907782, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907783, "dur": 100, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907884, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907886, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907888, "dur": 65, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907953, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701907955, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701907956, "dur": 60, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908016, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908165, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701908168, "dur": 94, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908263, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908266, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701908267, "dur": 63, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908334, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908340, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701908342, "dur": 143, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908485, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908635, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701908638, "dur": 118, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908759, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701908907, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701908909, "dur": 103, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909012, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909192, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701909194, "dur": 109, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909304, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909306, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701909307, "dur": 66, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909374, "dur": 16, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909532, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701909534, "dur": 92, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909627, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909801, "dur": 3, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701909810, "dur": 104, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701909916, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910113, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701910116, "dur": 111, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910228, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910232, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701910234, "dur": 71, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910306, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910498, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701910500, "dur": 101, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910601, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910604, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701910605, "dur": 67, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910673, "dur": 21, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701910841, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701910843, "dur": 162, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867701911006, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701911008, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701911010, "dur": 2306, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753867701913322, "dur": 7059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867701920532, "dur": 51, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867701920587, "dur": 40781037, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867742702467, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867742703126, "dur": 39, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867742703169, "dur": 41111498, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867783815641, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867783816023, "dur": 24, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867783816051, "dur": 40287750, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753867824104817, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824104903, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824104908, "dur": 225, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105136, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105214, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824105217, "dur": 148, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105366, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105482, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824105485, "dur": 166, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105654, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105733, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824105736, "dur": 144, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105883, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824105963, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824105967, "dur": 138, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106114, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106171, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824106175, "dur": 170, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106349, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106453, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824106457, "dur": 199, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106658, "dur": 8, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106734, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824106739, "dur": 255, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824106999, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824107122, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824107127, "dur": 219, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824107348, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824107457, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824107460, "dur": 229, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824107691, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824107792, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824107796, "dur": 313, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108113, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108218, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824108222, "dur": 268, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108493, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108599, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824108602, "dur": 227, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108831, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824108921, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824108924, "dur": 243, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824109171, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824109273, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824109277, "dur": 258, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824109538, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824109669, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824109673, "dur": 258, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824109933, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110028, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824110032, "dur": 274, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110308, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110401, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824110405, "dur": 231, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110640, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110741, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824110746, "dur": 226, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824110975, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111078, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824111083, "dur": 234, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111318, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111418, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824111422, "dur": 222, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111646, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111740, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824111744, "dur": 237, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824111983, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824112176, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824112180, "dur": 264, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824112449, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824112627, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824112633, "dur": 266, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824112903, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824113041, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824113046, "dur": 243, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824113293, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824113438, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824113443, "dur": 241, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824113688, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824113849, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824113853, "dur": 247, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114105, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114245, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824114248, "dur": 226, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114478, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114574, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824114583, "dur": 245, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114832, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824114943, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824114947, "dur": 242, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115193, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115291, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824115295, "dur": 232, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115531, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115632, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824115636, "dur": 226, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115865, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824115969, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824115972, "dur": 221, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824116197, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824116318, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824116322, "dur": 240, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824116565, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824116666, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824116669, "dur": 249, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824116923, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824117021, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824117026, "dur": 336, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824117366, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824117506, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824117510, "dur": 227, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824117739, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824117845, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824117849, "dur": 263, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118114, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118222, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824118226, "dur": 221, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118451, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118548, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824118552, "dur": 202, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118758, "dur": 10, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824118852, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824118856, "dur": 206, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824119065, "dur": 11, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824119169, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824119172, "dur": 272, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824119447, "dur": 13, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824119583, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824119589, "dur": 276, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824119867, "dur": 12, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824120018, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824120024, "dur": 401, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824120429, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824120537, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824120541, "dur": 251, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753867824120795, "dur": 8813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867824129757, "dur": 2, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867824129766, "dur": 40938081, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867865067857, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867865068040, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867865068047, "dur": 40271505, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867905339562, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867905339738, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753867905339742, "dur": 40733461, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753867946073212, "dur": 694464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867498451156, "dur": 1336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867498452494, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867498453323, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753867498453325, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867498453328, "dur": 732, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.AdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753867498454061, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_964EFA861597E925.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867498454121, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867498454123, "dur": 40273466, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_964EFA861597E925.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867538727602, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_DCFBB31C74AE1B02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867538727607, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867538727610, "dur": 40114669, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_DCFBB31C74AE1B02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867578842298, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_02752AD76BF9CA44.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867578842303, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867578842307, "dur": 40702060, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_02752AD76BF9CA44.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867619544381, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F92F05DAAA0885DC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867619544385, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867619544388, "dur": 40627217, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F92F05DAAA0885DC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867660171624, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5525CE4051F14881.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867660171629, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867660171633, "dur": 40236421, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5525CE4051F14881.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867700408074, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_FC20D6C350F635F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867700408081, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867700408087, "dur": 40248666, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_FC20D6C350F635F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867740656776, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753867740657389, "dur": 40, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867740657436, "dur": 41034128, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753867781693014, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867781693026, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867781693029, "dur": 40354019, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867822047058, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753867822047396, "dur": 23, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867822047423, "dur": 41067803, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753867863115962, "dur": 65996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863181963, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863181966, "dur": 324, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182293, "dur": 37, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182332, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863182334, "dur": 236, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182572, "dur": 49, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182623, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863182625, "dur": 206, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182832, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863182878, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863182880, "dur": 166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183047, "dur": 40, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183089, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863183091, "dur": 212, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183304, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183368, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863183370, "dur": 252, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183623, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867863183761, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867863183764, "dur": 40333809, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867903517591, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867903517895, "dur": 2, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867903517901, "dur": 40998519, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753867944516429, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944516490, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944516493, "dur": 249, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944516744, "dur": 33, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944516778, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944516780, "dur": 209, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944516991, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944517296, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944517298, "dur": 460, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944517760, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944517881, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944517883, "dur": 283, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518167, "dur": 34, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518203, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944518204, "dur": 216, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518421, "dur": 41, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518464, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944518466, "dur": 208, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518675, "dur": 40, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518724, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944518725, "dur": 221, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518947, "dur": 47, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944518996, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944518997, "dur": 185, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519183, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519253, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944519255, "dur": 198, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519454, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519579, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944519580, "dur": 298, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519880, "dur": 44, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944519926, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944519928, "dur": 230, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944520159, "dur": 41, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944520202, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944520204, "dur": 218, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944520423, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944520551, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944520553, "dur": 283, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944520837, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944521085, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944521086, "dur": 464, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944521551, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944521629, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944521640, "dur": 241, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944521882, "dur": 45, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944521929, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944521931, "dur": 225, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522157, "dur": 39, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522198, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944522200, "dur": 183, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522383, "dur": 27, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522412, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944522414, "dur": 153, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522568, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522624, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944522625, "dur": 178, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522803, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944522907, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944522908, "dur": 280, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944523189, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944523267, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944523268, "dur": 230, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Cinemachine.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944523499, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Cinemachine.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944523640, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944523641, "dur": 307, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Cinemachine.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944523949, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524008, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944524010, "dur": 211, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524222, "dur": 40, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524264, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944524265, "dur": 177, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524442, "dur": 41, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Management.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524485, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944524487, "dur": 166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Management.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524654, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Management.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524707, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944524709, "dur": 171, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Management.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524881, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.SpatialTracking.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944524912, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944524913, "dur": 150, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.SpatialTracking.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525065, "dur": 22, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525097, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944525098, "dur": 145, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525244, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525275, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944525276, "dur": 156, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525433, "dur": 24, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525458, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944525459, "dur": 145, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525605, "dur": 31, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525638, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944525639, "dur": 157, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525796, "dur": 30, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525828, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944525829, "dur": 163, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944525992, "dur": 44, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.CoreUtils.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526038, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944526039, "dur": 169, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.CoreUtils.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526209, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.CoreUtils.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526269, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944526270, "dur": 170, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.CoreUtils.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526441, "dur": 48, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526490, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944526491, "dur": 170, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526662, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526700, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944526701, "dur": 156, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526858, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944526923, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944526925, "dur": 199, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527125, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527266, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944527268, "dur": 286, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527556, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527615, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944527617, "dur": 202, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527820, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944527891, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944527900, "dur": 181, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944528082, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944528443, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944528444, "dur": 697, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944529144, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944529324, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944529325, "dur": 385, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944529712, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944529867, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944529869, "dur": 316, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944530187, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944530461, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944530463, "dur": 407, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944530871, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944530960, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944530962, "dur": 368, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944531331, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944531490, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944531492, "dur": 378, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944531871, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944531998, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944532000, "dur": 330, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944532331, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944532558, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944532560, "dur": 366, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944532927, "dur": 44, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944532973, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944532975, "dur": 224, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533200, "dur": 48, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533250, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944533251, "dur": 197, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VSCode.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533449, "dur": 37, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533487, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944533489, "dur": 171, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533660, "dur": 47, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533708, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944533710, "dur": 159, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533870, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944533948, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944533950, "dur": 202, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534153, "dur": 47, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534201, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944534203, "dur": 166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534370, "dur": 31, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534403, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944534405, "dur": 159, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534565, "dur": 28, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534595, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944534597, "dur": 175, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534773, "dur": 33, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534808, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944534809, "dur": 153, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534963, "dur": 31, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944534996, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944534997, "dur": 156, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944535154, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944535303, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944535304, "dur": 310, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944535615, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944535706, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944535708, "dur": 259, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944535968, "dur": 42, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Management.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536014, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944536016, "dur": 215, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Management.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536232, "dur": 39, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Management.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536274, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944536275, "dur": 173, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Management.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536449, "dur": 29, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536479, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944536481, "dur": 157, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536639, "dur": 31, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.SpatialTracking.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536671, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944536673, "dur": 153, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.SpatialTracking.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944536827, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944537044, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944537046, "dur": 353, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944537400, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944537518, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944537520, "dur": 259, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944537780, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944538315, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944538317, "dur": 813, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944539131, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944539327, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944539329, "dur": 381, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944539711, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944539769, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944539771, "dur": 230, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540002, "dur": 48, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540051, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944540053, "dur": 195, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540249, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540341, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944540343, "dur": 230, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540574, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540630, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944540631, "dur": 176, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540807, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944540919, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944540920, "dur": 392, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944541314, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944541415, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944541417, "dur": 272, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944541690, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944541747, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944541749, "dur": 241, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753867944541991, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944542069, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542071, "dur": 225, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753867944542297, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542302, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542306, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542311, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542316, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542320, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542324, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542328, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542331, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542336, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542340, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542343, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542347, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542352, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542355, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542359, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542362, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542366, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542370, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542373, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542377, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542380, "dur": 7, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542387, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542391, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542394, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542398, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542402, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542405, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542408, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542412, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542415, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542419, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542423, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542426, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542430, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542434, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542437, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542440, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542444, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542447, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542451, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542454, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542458, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542462, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542464, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542468, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542472, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542475, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542479, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542482, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542485, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542488, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542491, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542494, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542499, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542502, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542505, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542509, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542512, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542516, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542520, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542522, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542526, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542529, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542532, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542535, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542539, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542542, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542545, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542548, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542552, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542555, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542559, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542562, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542565, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542568, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542571, "dur": 7, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542578, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542582, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542586, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542589, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542593, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542596, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542600, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542602, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542605, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542608, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542612, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542614, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542617, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542689, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542692, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542695, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542698, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542702, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542706, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542710, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542712, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542716, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542720, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542723, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542727, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542730, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542733, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542738, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542741, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542744, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542747, "dur": 5, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542752, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542755, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542758, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542762, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542765, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542769, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542772, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542776, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542780, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542783, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542786, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542790, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542794, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542797, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542800, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542804, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542808, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542811, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542814, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542816, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542819, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542823, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542826, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542829, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542832, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542835, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542838, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542841, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542844, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542847, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542851, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542854, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542856, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542859, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542863, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542865, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542868, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542871, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542873, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542876, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542879, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542881, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542885, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542888, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542890, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542893, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542896, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542898, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542901, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542904, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542907, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542910, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542913, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542915, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542918, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542921, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542924, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542927, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542929, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542932, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542935, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542938, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542941, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542943, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542946, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542949, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542951, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542953, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542956, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542958, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542961, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542964, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542967, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542970, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542973, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542976, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542979, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542981, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542984, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542987, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542989, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542991, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542994, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542996, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944542999, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543002, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543006, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543008, "dur": 4, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543012, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543015, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543018, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543020, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543023, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543026, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543028, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543031, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543033, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543035, "dur": 3, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543038, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543040, "dur": 2, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543042, "dur": 1, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753867944543044, "dur": 2224598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498451182, "dur": 1403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498452587, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498452687, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498452770, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498452885, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498452971, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498453066, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753867498453316, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498453321, "dur": 762, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753867498454085, "dur": 30146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867498484396, "dur": 36, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867498484436, "dur": 40500242, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867538985881, "dur": 25178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867539011300, "dur": 62, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867539011367, "dur": 40558315, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867579571126, "dur": 26813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867579598069, "dur": 41, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867579598114, "dur": 40152621, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867619751691, "dur": 18825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867619770834, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867619770920, "dur": 41417686, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867661190860, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_6AF135979BD6EC55.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867661190867, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867661190870, "dur": 40136701, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_6AF135979BD6EC55.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867701327590, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867701328142, "dur": 33, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867701328179, "dur": 40914922, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867742243910, "dur": 15619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867742259657, "dur": 47, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867742259708, "dur": 40394851, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753867782656296, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XR.LegacyInputHelpers.ref.dll_FFA26A0728B3CF82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867782656310, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867782656314, "dur": 40704304, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.XR.LegacyInputHelpers.ref.dll_FFA26A0728B3CF82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867823360629, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.Interaction.Toolkit.ref.dll_96C656129EF9A05C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867823360638, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867823360642, "dur": 40713724, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.Interaction.Toolkit.ref.dll_96C656129EF9A05C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867864074378, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867864074587, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867864074590, "dur": 40282659, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867904357259, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867904357434, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753867904357438, "dur": 41103568, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753867945461017, "dur": 1306621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867498451199, "dur": 1662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867498452863, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867498453347, "dur": 25, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753867498453614, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867498453617, "dur": 922, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753867498454540, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_72B24756F7404064.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867498454653, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867498454655, "dur": 40971630, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_72B24756F7404064.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867539426300, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIWidgetsModule.dll_ADBA340CA29AB188.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867539426315, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867539426319, "dur": 40138694, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIWidgetsModule.dll_ADBA340CA29AB188.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867579565028, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_674E2B85739B41A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867579565034, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867579565037, "dur": 41071642, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_674E2B85739B41A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867620636692, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_724C118028FFF31E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867620636697, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867620636701, "dur": 40971465, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_724C118028FFF31E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867661608184, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_C47EF4C62911B5AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867661608190, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867661608194, "dur": 40138882, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_C47EF4C62911B5AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867701747098, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.AdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753867701747106, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867701747110, "dur": 131, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.AdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753867701747241, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753867701747643, "dur": 22, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867701747669, "dur": 40921261, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753867742669775, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753867742670251, "dur": 42, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867742670297, "dur": 40480816, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753867783152283, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867783152292, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867783152297, "dur": 40653892, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867823806199, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867823806286, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867823806291, "dur": 40402351, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867864208652, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867864208935, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867864208938, "dur": 40638173, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867904847127, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867904847417, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753867904847471, "dur": 40668601, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753867945516080, "dur": 1251573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498451217, "dur": 1670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498452889, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498452989, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498453076, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753867498453082, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498453453, "dur": 933, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.AdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753867498454389, "dur": 36287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867498490883, "dur": 45, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867498490932, "dur": 40634535, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867539126560, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A7C05568AAA94FF2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867539126565, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867539126569, "dur": 41035754, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A7C05568AAA94FF2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867580162338, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B5020B120F68CA5E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867580162342, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867580162345, "dur": 40709635, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B5020B120F68CA5E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867620871996, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D66E8C462D246403.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867620872000, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867620872004, "dur": 40341167, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D66E8C462D246403.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867661213199, "dur": 16334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867661229930, "dur": 99, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867661230033, "dur": 40741207, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867701973806, "dur": 37505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867702012022, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867702012107, "dur": 40868938, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867742883865, "dur": 26927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867742910950, "dur": 85, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867742911044, "dur": 40229682, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753867783141835, "dur": 16077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867783157921, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867783157928, "dur": 40276329, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867823434270, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867823434350, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867823434355, "dur": 40591986, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867864026351, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753867864026431, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867864026433, "dur": 326, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753867864026760, "dur": 170780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753867864197545, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867864197549, "dur": 443, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753867864197995, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867864198141, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867864198144, "dur": 40609027, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867904807181, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867904807349, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753867904807353, "dur": 40653864, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753867945461224, "dur": 1306431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867498451241, "dur": 2250, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867498453494, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867498453498, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867498453523, "dur": 1577, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867498455102, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B036A80B2A041587.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867498455159, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867498455162, "dur": 40126738, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B036A80B2A041587.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867538581914, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3D3F1192953EFAC3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867538581919, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867538581922, "dur": 40945156, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3D3F1192953EFAC3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867579527099, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_77E6E13E330826F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867579527105, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867579527108, "dur": 40957688, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_77E6E13E330826F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867620484810, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_429072FC079FCBE4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867620484815, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867620484818, "dur": 40284531, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_429072FC079FCBE4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769364, "dur": 45, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769725, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867660769729, "dur": 168, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769898, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769901, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867660769904, "dur": 72, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769977, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_D23447F8A6559783.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867660769995, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867660769999, "dur": 41029194, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_D23447F8A6559783.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867701799212, "dur": 36, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867701799550, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867701799555, "dur": 179, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867701799736, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867701799741, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867701799743, "dur": 82, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867701799826, "dur": 6631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753867701806734, "dur": 83, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867701806821, "dur": 41070826, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753867742879703, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742879913, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742879918, "dur": 204, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880124, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880265, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742880268, "dur": 118, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880387, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880390, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742880392, "dur": 76, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880470, "dur": 15, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880606, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742880607, "dur": 114, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880723, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880725, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742880727, "dur": 68, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753867742880797, "dur": 39471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867742920276, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867742920281, "dur": 40749269, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867783669566, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867783669681, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867783669686, "dur": 40310926, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867823980622, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867823980691, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867823980694, "dur": 41118294, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867865098998, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867865099169, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867865099173, "dur": 40289976, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867905389166, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867905389462, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753867905389468, "dur": 40653961, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753867946043438, "dur": 724227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867498451254, "dur": 2315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867498453571, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_87350FB78079A92E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867498453714, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867498453720, "dur": 40311952, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_87350FB78079A92E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867538765689, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D9761A15E13B957C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867538765694, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867538765698, "dur": 40734714, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D9761A15E13B957C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867579500429, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1057A6B1F30FF808.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867579500435, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867579500440, "dur": 40333341, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1057A6B1F30FF808.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867619833797, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DB28FF33EA4CED0F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867619833802, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867619833805, "dur": 41047851, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DB28FF33EA4CED0F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867660881670, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_3AF8D743E47F772D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867660881674, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867660881677, "dur": 40420247, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_3AF8D743E47F772D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867701301940, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753867701302688, "dur": 38, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867701302730, "dur": 42355411, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753867743659194, "dur": 18334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753867743677642, "dur": 44, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867743677690, "dur": 40573929, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753867784252577, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867784252586, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867784252590, "dur": 40899914, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867825152515, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867825152711, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867825152715, "dur": 40744437, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867865897161, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867865897325, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867865897331, "dur": 40485168, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867906382509, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867906382672, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753867906382679, "dur": 40371323, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753867946754019, "dur": 13633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867498451265, "dur": 2361, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867498453628, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_4F38519A48D8B514.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867498453741, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867498453743, "dur": 40237930, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_4F38519A48D8B514.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867538691687, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_95137CAAE52A61E4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867538691691, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867538691695, "dur": 40111253, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_95137CAAE52A61E4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867578802962, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C4846EE971D65013.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867578802967, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867578802970, "dur": 40309676, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C4846EE971D65013.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867619112660, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A7BDF878646DD76E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867619112665, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867619112681, "dur": 40865966, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A7BDF878646DD76E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867659978661, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_16D8B23382A72133.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867659978667, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867659978670, "dur": 40746495, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_16D8B23382A72133.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867700725179, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_52A52B01E8EB1B70.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867700725185, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867700725189, "dur": 40442634, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_52A52B01E8EB1B70.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867741167851, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867741168469, "dur": 39, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867741168514, "dur": 40949802, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867782119126, "dur": 18084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867782137511, "dur": 93, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867782137609, "dur": 40888119, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867823027937, "dur": 26226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867823054363, "dur": 61, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867823054428, "dur": 40828634, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753867863884219, "dur": 27045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.Interaction.Toolkit.Editor.ref.dll_13D11C44DBE92115.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867863911284, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867863911288, "dur": 40641214, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.Interaction.Toolkit.Editor.ref.dll_13D11C44DBE92115.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867904552513, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867904552681, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753867904552685, "dur": 40518618, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753867945071313, "dur": 1696323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867498451290, "dur": 2401, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867498453694, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_346CE69FAD609FC0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867498453766, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867498453768, "dur": 40693357, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_346CE69FAD609FC0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867539147140, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_86A05217779473DB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867539147145, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867539147149, "dur": 40452846, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_86A05217779473DB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867579600009, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PackageManagerUIModule.dll_936EC1D321D2D997.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867579600012, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867579600015, "dur": 40668684, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.PackageManagerUIModule.dll_936EC1D321D2D997.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867620268718, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_9DA5EC9BD8D71A7E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867620268722, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867620268726, "dur": 41019545, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_9DA5EC9BD8D71A7E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867661288289, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867661288309, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867661288313, "dur": 40119914, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408240, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408397, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701408401, "dur": 180, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408583, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408778, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701408780, "dur": 123, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408905, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408909, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701408911, "dur": 86, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701408999, "dur": 37, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409300, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701409302, "dur": 120, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409423, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409585, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701409591, "dur": 99, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409691, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409696, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701409698, "dur": 78, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409778, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409780, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701409781, "dur": 216, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701409999, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410004, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701410010, "dur": 205, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410218, "dur": 44, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410578, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701410582, "dur": 199, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410783, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410785, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701410787, "dur": 101, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410890, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410896, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701410898, "dur": 75, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.AdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1753867701410974, "dur": 23, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701411179, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701411182, "dur": 185, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753867701411370, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867701411395, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867701411399, "dur": 40885879, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867742297290, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753867742297656, "dur": 26, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867742297685, "dur": 40135658, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753867782434089, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753867782434430, "dur": 25, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867782434458, "dur": 40545051, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753867822981073, "dur": 14619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XR.LegacyInputHelpers.ref.dll_3F12D550ED6E7C7D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867822995712, "dur": 2, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867822995717, "dur": 40360283, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.XR.LegacyInputHelpers.ref.dll_3F12D550ED6E7C7D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867863356009, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867863356200, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867863356204, "dur": 40337146, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867903693359, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867903693523, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867903693526, "dur": 40662862, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867944356397, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867944356575, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753867944356580, "dur": 2302061, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753867946658650, "dur": 109051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867498451303, "dur": 2731, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867498454037, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7D4E79E3F8DF45D7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867498454088, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867498454090, "dur": 40452930, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7D4E79E3F8DF45D7.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867538907034, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E66F93169D2DD838.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867538907039, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867538907043, "dur": 40971998, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E66F93169D2DD838.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867579879057, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_E5A9F0377C0854DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867579879062, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867579879066, "dur": 40852360, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_E5A9F0377C0854DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867620731442, "dur": 7, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3637714A165C889B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867620731450, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867620731453, "dur": 40632989, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3637714A165C889B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867661364456, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8C288AE0AB45DDE9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867661364461, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867661364465, "dur": 41022716, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8C288AE0AB45DDE9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867702387198, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867702387207, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867702387211, "dur": 40465376, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867742852598, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867742852607, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867742852611, "dur": 40662636, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867783515257, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867783515333, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867783515336, "dur": 40982964, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867824498310, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867824498480, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867824498484, "dur": 40412543, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867864911035, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867864911208, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867864911212, "dur": 40243593, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867905154816, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867905155014, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753867905155017, "dur": 40703501, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753867945858527, "dur": 909130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867498451320, "dur": 2840, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867498454166, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_B633AB90CA8419F0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867498454247, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867498454249, "dur": 40769632, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_B633AB90CA8419F0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867539223904, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3F05803AFF2060AA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867539223909, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867539223915, "dur": 40498284, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_3F05803AFF2060AA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867579722221, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5F3E988FB30834DD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867579722228, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867579722232, "dur": 40638392, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_5F3E988FB30834DD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867620360638, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_71A9C7135D93ED8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867620360643, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867620360647, "dur": 40223800, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_71A9C7135D93ED8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867660584461, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIServiceModule.dll_141B1096D3843D29.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867660584465, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867660584469, "dur": 40966043, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIServiceModule.dll_141B1096D3843D29.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867701550529, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753867701550959, "dur": 27, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867701550991, "dur": 40799288, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753867742351095, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753867742351470, "dur": 23, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867742351497, "dur": 40856626, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753867783208993, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.Management.Editor.ref.dll_7BA8CC5454F13E52.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867783209083, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867783209087, "dur": 40286783, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.Management.Editor.ref.dll_7BA8CC5454F13E52.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867823495880, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpatialTracking.ref.dll_8911EA67839E93F0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867823495946, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867823495951, "dur": 40357545, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.SpatialTracking.ref.dll_8911EA67839E93F0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867863853505, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867863853691, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867863853695, "dur": 40210736, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867904064448, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867904064766, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867904064777, "dur": 40291583, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867944356378, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867944356710, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753867944356714, "dur": 2342377, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753867946699107, "dur": 68602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867498451333, "dur": 3099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867498454439, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B805775722B84DE5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867498454752, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867498454756, "dur": 40937934, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B805775722B84DE5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867539392704, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_71B24863D01BCA9B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867539392709, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867539392712, "dur": 40146364, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_71B24863D01BCA9B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867579539092, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_1B431206FE54F13A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867579539097, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867579539100, "dur": 40187433, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_1B431206FE54F13A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867619726549, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_8DA7AA008A6BEF63.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867619726553, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867619726557, "dur": 40812388, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_8DA7AA008A6BEF63.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867660538966, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_F84A6C2881805431.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867660538971, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867660538975, "dur": 40695193, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_F84A6C2881805431.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234182, "dur": 18, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234348, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701234351, "dur": 167, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234519, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234521, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701234523, "dur": 77, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234601, "dur": 19, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234789, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701234791, "dur": 183, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234976, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701234979, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701234984, "dur": 183, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701235168, "dur": 48, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701235722, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701235727, "dur": 266, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701235996, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236000, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701236015, "dur": 171, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236188, "dur": 25, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236354, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701236357, "dur": 146, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236506, "dur": 17, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236659, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701236661, "dur": 119, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236781, "dur": 2, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236785, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701236787, "dur": 72, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701236861, "dur": 14, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237019, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701237021, "dur": 96, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237118, "dur": 0, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237125, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701237126, "dur": 76, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237202, "dur": 20, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237377, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701237379, "dur": 115, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237495, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237498, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701237500, "dur": 73, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237575, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237577, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701237579, "dur": 197, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.AdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1753867701237780, "dur": 5285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867701243329, "dur": 105, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867701243446, "dur": 41266577, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867742511153, "dur": 17068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867742528543, "dur": 80, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867742528627, "dur": 41373854, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867783904202, "dur": 27958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867783932351, "dur": 51, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867783932407, "dur": 40388479, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753867824321982, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753867824322095, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867824322105, "dur": 1050, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/com.unity.cinemachine.editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1753867824323158, "dur": 465947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/com.unity.cinemachine.editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753867824789111, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867824789115, "dur": 471, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/com.unity.cinemachine.editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753867824789588, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867824789756, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867824789759, "dur": 40355219, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867865144988, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867865145151, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867865145155, "dur": 40848050, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867905993218, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867905993428, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753867905993432, "dur": 40333207, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753867946326651, "dur": 441019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867498451350, "dur": 3210, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867498454564, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6F854AC92E9881D0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867498454624, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867498454626, "dur": 40361101, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6F854AC92E9881D0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867538815813, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_2BF52A46E4BB6A5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867538815820, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867538815825, "dur": 40949926, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_2BF52A46E4BB6A5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867579765765, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_3B47A9D25B701326.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867579765770, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867579765774, "dur": 40930300, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_3B47A9D25B701326.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867620696088, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0E7AF3440F83AD09.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867620696093, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867620696100, "dur": 40954847, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0E7AF3440F83AD09.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867661650964, "dur": 9, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_AA2FF738B9160384.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867661650974, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867661650977, "dur": 40596036, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_AA2FF738B9160384.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867702247034, "dur": 2201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867702249642, "dur": 117, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867702249763, "dur": 41935634, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867744187892, "dur": 29254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867744217373, "dur": 65, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867744217443, "dur": 41405001, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867785623832, "dur": 23249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867785647247, "dur": 55, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867785647306, "dur": 40475275, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867826123698, "dur": 15410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867826139221, "dur": 35, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867826139260, "dur": 41063541, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867867203571, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867867203900, "dur": 26, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867867203929, "dur": 40864659, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753867908070430, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867908070714, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753867908070718, "dur": 38666982, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753867946737715, "dur": 29979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867498451367, "dur": 3984, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867498455355, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_0B464E8CF7A7A47E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867498455415, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867498455417, "dur": 40475809, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_0B464E8CF7A7A47E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867538931244, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_CA644BDA1BE3ACA2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867538931249, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867538931253, "dur": 40341405, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_CA644BDA1BE3ACA2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867579272671, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_3BE2369E45393CD8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867579272675, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867579272679, "dur": 40435304, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_3BE2369E45393CD8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867619707997, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F74331EDFB109D71.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867619708002, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867619708005, "dur": 40659117, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F74331EDFB109D71.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867660367141, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_47FEC76562BF022D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867660367147, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867660367151, "dur": 40566230, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_47FEC76562BF022D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867700933396, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_2D2FD4BF9B17C852.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867700933401, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867700933404, "dur": 40344940, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_2D2FD4BF9B17C852.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867741278361, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpatialTracking.ref.dll_1552DC8EFC971715.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867741278371, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867741278374, "dur": 40376064, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SpatialTracking.ref.dll_1552DC8EFC971715.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867781654455, "dur": 6, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 175386**********, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867781654474, "dur": 40205352, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867821859839, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753867821860283, "dur": 31, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867821860318, "dur": 40752551, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753867862613750, "dur": 17092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867862630849, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867862630854, "dur": 40742969, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867903373843, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867903374198, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867903374204, "dur": 40645159, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867944019378, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867944019673, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753867944019676, "dur": 2544927, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753867946564612, "dur": 203072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867498451378, "dur": 4096, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867498455586, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_5B7884104A6F8898.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867498455702, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867498455704, "dur": 40212211, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_5B7884104A6F8898.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867538667931, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F132DEBB602BA099.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867538667936, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867538667940, "dur": 40765236, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F132DEBB602BA099.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867579433194, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_98D417618B6E43AD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867579433198, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867579433203, "dur": 40619346, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_98D417618B6E43AD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867620052568, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5E9C37C961B05C37.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867620052574, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867620052578, "dur": 40473454, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5E9C37C961B05C37.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867660526051, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_763EAA3673D4C4EF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867660526057, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867660526061, "dur": 40249347, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_763EAA3673D4C4EF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867700775423, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867700775430, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867700775434, "dur": 40745961, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867741521410, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753867741521792, "dur": 29, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867741521824, "dur": 41031091, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753867782553834, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.XR.CoreUtils.ref.dll_3CA2B461938B5BFC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867782553862, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867782553867, "dur": 40368189, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.XR.CoreUtils.ref.dll_3CA2B461938B5BFC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867822922066, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867822922075, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867822922078, "dur": 40960722, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867863882813, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867863883008, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867863883011, "dur": 40397788, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867904280815, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867904281084, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753867904281088, "dur": 40998570, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753867945279667, "dur": 1487996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867498451400, "dur": 4396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867498455799, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_BC88F8DBCB1121D9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867498455867, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867498455870, "dur": 40600614, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_BC88F8DBCB1121D9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867539056499, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_D42F17825A313884.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867539056504, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867539056507, "dur": 40187253, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_D42F17825A313884.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867579243773, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_5344349B3CA0EC43.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867579243777, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867579243781, "dur": 40244125, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_5344349B3CA0EC43.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867619487919, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3D6C4FE8500D3086.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867619487923, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867619487926, "dur": 40596492, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3D6C4FE8500D3086.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867660084445, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2FB16F39D0F223B8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867660084452, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867660084457, "dur": 40605233, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2FB16F39D0F223B8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867700689706, "dur": 1, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753867700689710, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867700689714, "dur": 147, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.AdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753867700689862, "dur": 49, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753867700690191, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867700690194, "dur": 134, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753867700690329, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867700690335, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867700690338, "dur": 40343475, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867741033826, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753867741034410, "dur": 41, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867741034455, "dur": 40282172, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753867781317541, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867781317550, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867781317554, "dur": 40676553, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867821994118, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867821994140, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867821994143, "dur": 40202435, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867862196591, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867862196600, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867862196605, "dur": 40578413, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867902775029, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867902775037, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867902775041, "dur": 40353500, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867943128553, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753867943128808, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753867943128812, "dur": 3638756, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867498451413, "dur": 4524, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867498455937, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_09BABB90A178FFBF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867498455998, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867498456001, "dur": 40169384, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_09BABB90A178FFBF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867538625408, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_8CF8C2C5CCFE8952.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867538625464, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867538625472, "dur": 40114749, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_8CF8C2C5CCFE8952.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867578740238, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_91AD5E4C0FCDCCA6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867578740243, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867578740247, "dur": 40903351, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_91AD5E4C0FCDCCA6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867619643620, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_8140AE335778E0D9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867619643627, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867619643632, "dur": 40309144, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_8140AE335778E0D9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867659952790, "dur": 4, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_7DC10F2E5CC40017.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867659952794, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867659952797, "dur": 40505754, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_7DC10F2E5CC40017.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867700458567, "dur": 5, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9F2C7B6B1B06A08C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867700458574, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867700458578, "dur": 40294800, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9F2C7B6B1B06A08C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867740753397, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753867740753906, "dur": 38, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867740753952, "dur": 40686248, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753867781441082, "dur": 3, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867781441104, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867781441108, "dur": 40590187, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867822031312, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867822031445, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867822031450, "dur": 40508914, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867862540384, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867862540585, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867862540588, "dur": 40727526, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867903268133, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753867903268250, "dur": 1, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867903268256, "dur": 499, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753867903268757, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753867903268813, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867903268817, "dur": 238, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1753867903269058, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867903269254, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867903269257, "dur": 40750217, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867944019484, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867944019649, "dur": 0, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753867944019652, "dur": 2608018, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753867946627680, "dur": 140022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753867946771142, "dur": 2717, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 0, "tid": 1, "ts": 1753867946800791, "dur": 1900, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 0, "tid": 1, "ts": 1753867946798543, "dur": 4168, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}